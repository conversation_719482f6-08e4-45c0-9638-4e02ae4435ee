<template>
  <div class="labor-home">
    <SearchHeader v-model="searchKeyword" placeholder="搜索商品" :redirect-to-search="true" redirect-url="/search"
      @search="handleSearch" />

    <div class="banner-container">
      <transition name="skeleton-fade" mode="out-in">
        <BannerSkeleton v-if="skeletonStates.banner" key="banner-skeleton" />
        <GoodsImageSwiper v-else-if="headerBannerList.length > 0" key="banner-content"
          :media-list="headerBannerList" mode="banner" paginationType="fraction"
          :autoplay="true" :loop="true" @image-click="handleBannerClick" />
      </transition>
    </div>

    <div class="grid-menu-container">
      <transition name="skeleton-fade" mode="out-in">
        <GridMenuSkeleton v-if="skeletonStates.gridMenu" key="grid-skeleton" />
        <IconGrid v-else-if="gridMenuItems.length > 0" key="grid-content"
          :items="gridMenuItems" :columns="5" :show-more="true" :max-items="10"
          @item-click="handleGridItemClick" @more-click="handleMoreClick" />
      </transition>
    </div>

    <SectionContainer title="各县销冠" ref="limitedBlockRef">
      <van-list :loading="false" :finished="true" :immediate-check="false">
        <div class="waterfall-container">
          <transition name="waterfall-fade" mode="out-in">
            <WaterfallSkeleton v-if="skeletonStates.limited" :skeleton-count="6" key="limited-skeleton" />
            <Waterfall v-else-if="limitedList.length > 0" ref="limitedWaterfallRef" key="limited-waterfall"
              :list="limitedList" :breakpoints="breakpoints" :hasAroundGutter="false" :animationDuration="0"
              :animationDelay="0" :backgroundColor="'transparent'" :horizontalOrder="true" :lazyload="true">
              <template #default="{ item }">
                <ProductCard :key="item.goodsId" :goods-info="item" @click="handleGoodsClick(item)" />
              </template>
            </Waterfall>
          </transition>
        </div>

        <div class="load-more-container" v-if="limitedButtonCanShow && !limitedFinished && limitedIsFirstLoadComplete">
          <WoButton class="load-more-button" type="text" :loading="limitedLoading" :disabled="limitedLoading"
            @click="handleLimitedLoadMore">
            {{ limitedLoading ? '加载中...' : '加载更多' }}
          </WoButton>
        </div>

        <transition name="fade-up">
          <div class="no-more-text" v-if="limitedList.length > 0 && limitedFinished && !skeletonStates.limited">
            <span>没有更多了</span>
          </div>
        </transition>
      </van-list>
    </SectionContainer>

    <SectionContainer v-if="moduleShowStates.newer" title="新上好物" ref="newerBlockRef">
      <div class="horizontal-scroll-container">
        <transition name="skeleton-fade" mode="out-in">
          <HorizontalScrollSkeleton v-if="skeletonStates.newer" :skeleton-count="5" key="newer-skeleton" />
          <div v-else-if="newerList.length > 0" key="newer-content" class="horizontal-scroll-wrapper">
            <div class="goods-item" v-for="item in newerList" :key="item.goodsId" @click="handleGoodsClick(item)">
              <ProductCard :goods-info="item" @click="handleGoodsClick(item)" />
            </div>
          </div>
        </transition>
      </div>
    </SectionContainer>

    <SectionContainer v-if="moduleShowStates.hotProducts" title="爆款好物" ref="hotProductsBlockRef">
      <van-list :loading="false" :finished="true" :immediate-check="false">
        <div class="waterfall-container">
          <transition name="waterfall-fade" mode="out-in">
            <WaterfallSkeleton v-if="skeletonStates.hotProducts" :skeleton-count="6" key="hot-skeleton" />
            <Waterfall v-else-if="hotProductsList.length > 0" ref="hotProductsWaterfallRef" key="hot-waterfall"
              :list="hotProductsList" :breakpoints="breakpoints" :hasAroundGutter="false" :animationDuration="0"
              :animationDelay="0" :backgroundColor="'transparent'" :horizontalOrder="true" :lazyload="true">
              <template #default="{ item }">
                <ProductCard :key="item.goodsId" :goods-info="item" @click="handleGoodsClick(item)" />
              </template>
            </Waterfall>
          </transition>
        </div>

        <div class="load-more-container"
          v-if="hotProductsButtonCanShow && !hotProductsFinished && hotProductsIsFirstLoadComplete">
          <WoButton class="load-more-button" type="text" :loading="hotProductsLoading" :disabled="hotProductsLoading"
            @click="handleHotProductsLoadMore">
            {{ hotProductsLoading ? '加载中...' : '加载更多' }}
          </WoButton>
        </div>

        <transition name="fade-up">
          <div class="no-more-text" v-if="hotProductsList.length > 0 && hotProductsFinished && !skeletonStates.hotProducts">
            <span>没有更多了</span>
          </div>
        </transition>
      </van-list>
    </SectionContainer>
  </div>
</template>
